-- 销售出库明细账表结构 (使用原始字段名称)
-- 基于20250106销售出库明细账.xlsx文件创建

CREATE DATABASE IF NOT EXISTS sales_management 
CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;

USE sales_management;

-- 销售出库明细表 (原始字段名称)
CREATE TABLE `销售出库明细账` (
    `id` BIGINT AUTO_INCREMENT PRIMARY KEY COMMENT '主键ID',
    
    -- 订单基本信息
    `订单编号` VARCHAR(225) NOT NULL COMMENT '订单编号',
    `原始单号` VARCHAR(225) COMMENT '原始单号',
    `子单原始单号` BIGINT COMMENT '子单原始单号',
    `原始子订单号` VARCHAR(225) COMMENT '原始子订单号',
    `订单类型` VARCHAR(225) COMMENT '订单类型',
    `支付账号` VARCHAR(225) COMMENT '支付账号',

    -- 出库信息
    `出库单编号` VARCHAR(225) NOT NULL COMMENT '出库单编号',
    `仓库` VARCHAR(225) COMMENT '仓库',
    `仓库类型` VARCHAR(225) COMMENT '仓库类型',
    `店铺` VARCHAR(225) COMMENT '店铺',
    `出库单状态` VARCHAR(225) COMMENT '出库单状态',
    `出库状态` VARCHAR(225) COMMENT '出库状态',
    `分拣序号` INT COMMENT '分拣序号',
    
    -- 商品信息
    `商家编码` VARCHAR(225) COMMENT '商家编码',
    `货品编号` VARCHAR(225) COMMENT '货品编号',
    `货品名称` VARCHAR(225) COMMENT '货品名称',
    `货品简称` VARCHAR(225) COMMENT '货品简称',
    `品牌` VARCHAR(225) COMMENT '品牌',
    `分类` VARCHAR(225) COMMENT '分类',
    `规格码` VARCHAR(225) COMMENT '规格码',
    `规格名称` VARCHAR(225) COMMENT '规格名称',
    `平台货品名称` VARCHAR(225) COMMENT '平台货品名称',
    `平台规格名称` VARCHAR(225) COMMENT '平台规格名称',
    `平台货品ID` VARCHAR(225) COMMENT '平台货品ID',
    `平台规格ID` VARCHAR(225) COMMENT '平台规格ID',
    `条形码` VARCHAR(225) COMMENT '条形码',
    
    -- 数量和价格信息
    `货品数量` INT NOT NULL COMMENT '货品数量',
    `货品原单价` DECIMAL(10,2) COMMENT '货品原单价',
    `货品原总金额` DECIMAL(10,2) COMMENT '货品原总金额',
    `订单总优惠` DECIMAL(10,2) COMMENT '订单总优惠',
    `邮费` DECIMAL(10,2) COMMENT '邮费',
    `货品成交价` DECIMAL(10,2) COMMENT '货品成交价',
    `货品成交总价` DECIMAL(10,2) COMMENT '货品成交总价',
    `货品总优惠` DECIMAL(10,2) COMMENT '货品总优惠',
    `货到付款金额` DECIMAL(10,2) COMMENT '货到付款金额',
    
    -- 成本信息
    `货品成本` DECIMAL(10,2) COMMENT '货品成本',
    `货品总成本` DECIMAL(10,2) COMMENT '货品总成本',
    `固定成本` DECIMAL(10,2) COMMENT '固定成本',
    `固定总成本` DECIMAL(10,2) COMMENT '固定总成本',
    
    -- 支付信息
    `订单支付金额` DECIMAL(10,2) COMMENT '订单支付金额',
    `应收金额` DECIMAL(10,2) COMMENT '应收金额',
    `退款前支付金额` DECIMAL(10,2) COMMENT '退款前支付金额',
    `单品支付金额` DECIMAL(10,2) COMMENT '单品支付金额',
    `分摊邮费` DECIMAL(10,2) COMMENT '分摊邮费',
    `预估邮资` DECIMAL(10,2) COMMENT '预估邮资',
    `邮资成本` DECIMAL(10,2) COMMENT '邮资成本',
    `订单包装成本` DECIMAL(10,2) COMMENT '订单包装成本',
    
    -- 利润信息
    `订单毛利` DECIMAL(10,2) COMMENT '订单毛利',
    `毛利率` DECIMAL(5,2) COMMENT '毛利率',
    `订单固定毛利` DECIMAL(10,2) COMMENT '订单固定毛利',
    `固定毛利率` DECIMAL(5,2) COMMENT '固定毛利率',
    
    -- 客户信息
    `客户网名` VARCHAR(225) COMMENT '客户网名',
    `收件人` VARCHAR(225) COMMENT '收件人',
    `证件号码` VARCHAR(225) COMMENT '证件号码',
    `收货地区` VARCHAR(225) COMMENT '收货地区',
    `收货地址` VARCHAR(225) COMMENT '收货地址',
    `收件人手机` VARCHAR(225) COMMENT '收件人手机',
    `收件人电话` VARCHAR(225) COMMENT '收件人电话',

    -- 物流信息
    `物流公司` VARCHAR(225) COMMENT '物流公司',
    `实际重量` DECIMAL(8,2) COMMENT '实际重量',
    `预估重量` DECIMAL(8,2) COMMENT '预估重量',
    `需开发票` VARCHAR(225) COMMENT '需开发票',
    
    -- 操作人员信息
    `制单人` VARCHAR(225) COMMENT '制单人',
    `打单员` VARCHAR(225) COMMENT '打单员',
    `拣货员` VARCHAR(225) COMMENT '拣货员',
    `打包员` VARCHAR(225) COMMENT '打包员',
    `检视员` VARCHAR(225) COMMENT '检视员',
    `业务员` VARCHAR(225) COMMENT '业务员',
    `验货员` VARCHAR(225) COMMENT '验货员',

    -- 打印信息
    `打印波次` VARCHAR(225) COMMENT '打印波次',
    `物流单打印状态` VARCHAR(225) COMMENT '物流单打印状态',
    `发货单打印状态` VARCHAR(225) COMMENT '发货单打印状态',
    `分拣单打印状态` VARCHAR(225) COMMENT '分拣单打印状态',
    `物流单号` VARCHAR(225) COMMENT '物流单号',
    `分拣单编号` VARCHAR(225) COMMENT '分拣单编号',
    `外部单号` VARCHAR(225) COMMENT '外部单号',
    
    -- 时间信息
    `付款时间` DATETIME COMMENT '付款时间',
    `发货时间` DATETIME COMMENT '发货时间',
    `下单时间` DATETIME COMMENT '下单时间',
    `审核时间` DATETIME COMMENT '审核时间',
    
    -- 其他信息
    `赠品方式` VARCHAR(225) COMMENT '赠品方式',
    `买家留言` VARCHAR(225) COMMENT '买家留言',
    `客服备注` VARCHAR(225) COMMENT '客服备注',
    `打印备注` TEXT COMMENT '打印备注',
    `备注` TEXT COMMENT '备注',
    `包装` VARCHAR(225) COMMENT '包装',
    `来源组合装编码` VARCHAR(225) COMMENT '来源组合装编码',
    `拆自组合装` VARCHAR(225) COMMENT '拆自组合装',
    `来源组合装数量` INT COMMENT '来源组合装数量',
    `体积` DECIMAL(10,2) COMMENT '体积',
    `分销商` VARCHAR(225) COMMENT '分销商',
    `分销商编号` VARCHAR(225) COMMENT '分销商编号',
    `分销原始单号` VARCHAR(225) COMMENT '分销原始单号',
    
    -- 创建时间和更新时间
    `创建时间` TIMESTAMP DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    `更新时间` TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    
    -- 索引
    INDEX `idx_订单编号` (`订单编号`),
    INDEX `idx_出库单编号` (`出库单编号`),
    INDEX `idx_货品编号` (`货品编号`),
    INDEX `idx_店铺` (`店铺`),
    INDEX `idx_仓库` (`仓库`),
    INDEX `idx_发货时间` (`发货时间`),
    INDEX `idx_下单时间` (`下单时间`),
    INDEX `idx_审核时间` (`审核时间`),
    INDEX `idx_物流单号` (`物流单号`)
    
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='销售出库明细表';

-- 数据导入示例SQL (可选)
-- 如果需要从Excel导入数据，可以先将Excel转换为CSV，然后使用以下语句：
/*
LOAD DATA INFILE 'path/to/your/file.csv'
INTO TABLE `销售出库明细账`
FIELDS TERMINATED BY ','
ENCLOSED BY '"'
LINES TERMINATED BY '\n'
IGNORE 1 ROWS
(`订单编号`, `原始单号`, `子单原始单号`, `原始子订单号`, `订单类型`, `支付账号`,
 `出库单编号`, `仓库`, `仓库类型`, `店铺`, `出库单状态`, `出库状态`, `分拣序号`,
 `商家编码`, `货品编号`, `货品名称`, `货品简称`, `品牌`, `分类`, `规格码`, `规格名称`,
 `平台货品名称`, `平台规格名称`, `平台货品ID`, `平台规格ID`, `条形码`,
 `货品数量`, `货品原单价`, `货品原总金额`, `订单总优惠`, `邮费`, `货品成交价`,
 `货品成交总价`, `货品总优惠`, `货到付款金额`, `货品成本`, `货品总成本`,
 `固定成本`, `固定总成本`, `订单支付金额`, `应收金额`, `退款前支付金额`,
 `单品支付金额`, `分摊邮费`, `预估邮资`, `邮资成本`, `订单包装成本`,
 `订单毛利`, `毛利率`, `订单固定毛利`, `固定毛利率`, `客户网名`, `收件人`,
 `证件号码`, `收货地区`, `收货地址`, `收件人手机`, `收件人电话`, `物流公司`,
 `实际重量`, `预估重量`, `需开发票`, `制单人`, `打单员`, `拣货员`, `打包员`,
 `检视员`, `业务员`, `验货员`, `打印波次`, `物流单打印状态`, `发货单打印状态`,
 `分拣单打印状态`, `物流单号`, `分拣单编号`, `外部单号`, `付款时间`, `发货时间`,
 `赠品方式`, `买家留言`, `客服备注`, `打印备注`, `备注`, `包装`, `来源组合装编码`,
 `拆自组合装`, `来源组合装数量`, `体积`, `分销商`, `分销商编号`, `分销原始单号`,
 `下单时间`, `审核时间`);
*/
